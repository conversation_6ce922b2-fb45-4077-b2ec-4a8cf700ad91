# سكريبت PowerShell لنشر Universal AI Assistants

Write-Host "🚀 بدء النشر السريع..." -ForegroundColor Green

# تعيين المتغيرات
$PROJECT_ID = "universal-ai-assistants-2025"
$SERVICE_NAME = "universal-ai-assistants"
$REGION = "us-central1"

Write-Host "📋 إعداد المشروع..." -ForegroundColor Yellow
gcloud config set project $PROJECT_ID

Write-Host "🔧 تفعيل الخدمات..." -ForegroundColor Yellow
gcloud services enable run.googleapis.com
gcloud services enable cloudbuild.googleapis.com

Write-Host "🐳 نشر الخدمة..." -ForegroundColor Yellow
gcloud run deploy $SERVICE_NAME `
  --source . `
  --region $REGION `
  --allow-unauthenticated `
  --memory 2Gi `
  --cpu 2 `
  --max-instances 10 `
  --set-env-vars ENVIRONMENT=production `
  --set-env-vars PROJECT_ID=$PROJECT_ID

Write-Host "✅ تم النشر بنجاح!" -ForegroundColor Green
Write-Host "🔗 رابط الخدمة:" -ForegroundColor Cyan
gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)"

Read-Host "اضغط Enter للمتابعة"
