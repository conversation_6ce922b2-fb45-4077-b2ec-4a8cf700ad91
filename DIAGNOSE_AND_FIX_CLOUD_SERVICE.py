#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 تشخيص وإصلاح خدمة Google Cloud
تاريخ الإنشاء: 2025-01-31
الهدف: تشخيص مشكلة "Service Unavailable" وإصلاحها
"""

import subprocess
import json
import time
from datetime import datetime
from pathlib import Path

class CloudServiceDiagnostic:
    def __init__(self):
        self.project_id = "universal-ai-assistants-2025"
        self.service_name = "universal-ai-assistants"
        self.region = "us-central1"
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
    def print_banner(self):
        """عرض شعار التشخيص"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║         🔧 تشخيص وإصلاح خدمة Google Cloud 🔧               ║
║                                                              ║
║    🌐 Universal AI Assistants - Service Diagnostic          ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
        print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🆔 المشروع: {self.project_id}")
        print(f"🏃‍♂️ الخدمة: {self.service_name}")
        print(f"📍 المنطقة: {self.region}")
        print("=" * 70)

    def run_gcloud_command(self, command):
        """تشغيل أمر gcloud وإرجاع النتيجة"""
        try:
            result = subprocess.run(
                command, 
                shell=True, 
                capture_output=True, 
                text=True, 
                timeout=60
            )
            return {
                "success": result.returncode == 0,
                "stdout": result.stdout.strip(),
                "stderr": result.stderr.strip(),
                "returncode": result.returncode
            }
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "stdout": "",
                "stderr": "Command timed out",
                "returncode": -1
            }
        except Exception as e:
            return {
                "success": False,
                "stdout": "",
                "stderr": str(e),
                "returncode": -1
            }

    def check_gcloud_auth(self):
        """فحص حالة المصادقة"""
        print("🔐 فحص حالة المصادقة...")
        
        # فحص الحساب النشط
        result = self.run_gcloud_command("gcloud auth list --filter=status:ACTIVE --format='value(account)'")
        
        if result["success"] and result["stdout"]:
            print(f"  ✅ مصادق كـ: {result['stdout']}")
            return True
        else:
            print("  ❌ غير مصادق أو مشكلة في المصادقة")
            print("  💡 الحل: gcloud auth login")
            return False

    def check_project_config(self):
        """فحص إعداد المشروع"""
        print("🆔 فحص إعداد المشروع...")
        
        result = self.run_gcloud_command("gcloud config get-value project")
        
        if result["success"] and result["stdout"] == self.project_id:
            print(f"  ✅ المشروع مُعد بشكل صحيح: {result['stdout']}")
            return True
        else:
            print(f"  ⚠️ المشروع الحالي: {result['stdout']}")
            print(f"  💡 الحل: gcloud config set project {self.project_id}")
            return False

    def check_service_status(self):
        """فحص حالة الخدمة"""
        print("🏃‍♂️ فحص حالة الخدمة...")
        
        command = f"gcloud run services describe {self.service_name} --region={self.region} --format=json"
        result = self.run_gcloud_command(command)
        
        if result["success"]:
            try:
                service_data = json.loads(result["stdout"])
                
                # استخراج معلومات الحالة
                status = service_data.get("status", {})
                conditions = status.get("conditions", [])
                
                print(f"  📊 معلومات الخدمة:")
                print(f"    🔗 URL: {status.get('url', 'غير متاح')}")
                
                # فحص الشروط
                for condition in conditions:
                    condition_type = condition.get("type", "Unknown")
                    condition_status = condition.get("status", "Unknown")
                    reason = condition.get("reason", "")
                    message = condition.get("message", "")
                    
                    status_icon = "✅" if condition_status == "True" else "❌"
                    print(f"    {status_icon} {condition_type}: {condition_status}")
                    
                    if reason:
                        print(f"      📝 السبب: {reason}")
                    if message:
                        print(f"      💬 الرسالة: {message}")
                
                # فحص آخر نشر
                metadata = service_data.get("metadata", {})
                creation_timestamp = metadata.get("creationTimestamp", "غير معروف")
                print(f"    📅 تاريخ الإنشاء: {creation_timestamp}")
                
                return service_data
                
            except json.JSONDecodeError:
                print("  ❌ خطأ في تحليل بيانات الخدمة")
                return None
        else:
            print(f"  ❌ فشل في الحصول على معلومات الخدمة: {result['stderr']}")
            return None

    def check_service_logs(self):
        """فحص سجلات الخدمة"""
        print("📋 فحص سجلات الخدمة...")
        
        command = f'gcloud logs read "resource.type=cloud_run_revision AND resource.labels.service_name={self.service_name}" --limit=10 --format=json'
        result = self.run_gcloud_command(command)
        
        if result["success"]:
            try:
                if result["stdout"]:
                    logs = json.loads(result["stdout"])
                    print(f"  📊 عدد السجلات الأخيرة: {len(logs)}")
                    
                    for i, log_entry in enumerate(logs[:5]):  # أول 5 سجلات
                        timestamp = log_entry.get("timestamp", "غير معروف")
                        severity = log_entry.get("severity", "INFO")
                        text_payload = log_entry.get("textPayload", "")
                        
                        severity_icon = {
                            "ERROR": "🔴",
                            "WARNING": "🟡", 
                            "INFO": "🔵",
                            "DEBUG": "⚪"
                        }.get(severity, "⚪")
                        
                        print(f"    {severity_icon} [{timestamp}] {severity}: {text_payload[:100]}...")
                else:
                    print("  ⚠️ لا توجد سجلات متاحة")
                    
            except json.JSONDecodeError:
                print("  ❌ خطأ في تحليل السجلات")
        else:
            print(f"  ❌ فشل في الحصول على السجلات: {result['stderr']}")

    def check_service_revisions(self):
        """فحص إصدارات الخدمة"""
        print("📦 فحص إصدارات الخدمة...")
        
        command = f"gcloud run revisions list --service={self.service_name} --region={self.region} --format=json"
        result = self.run_gcloud_command(command)
        
        if result["success"]:
            try:
                if result["stdout"]:
                    revisions = json.loads(result["stdout"])
                    print(f"  📊 عدد الإصدارات: {len(revisions)}")
                    
                    for revision in revisions[:3]:  # أول 3 إصدارات
                        name = revision.get("metadata", {}).get("name", "غير معروف")
                        creation_time = revision.get("metadata", {}).get("creationTimestamp", "غير معروف")
                        
                        # فحص حالة الإصدار
                        status = revision.get("status", {})
                        conditions = status.get("conditions", [])
                        
                        ready_condition = next((c for c in conditions if c.get("type") == "Ready"), {})
                        ready_status = ready_condition.get("status", "Unknown")
                        
                        status_icon = "✅" if ready_status == "True" else "❌"
                        print(f"    {status_icon} {name} ({creation_time})")
                        
                        if ready_status != "True":
                            reason = ready_condition.get("reason", "")
                            message = ready_condition.get("message", "")
                            if reason:
                                print(f"      📝 السبب: {reason}")
                            if message:
                                print(f"      💬 الرسالة: {message}")
                else:
                    print("  ⚠️ لا توجد إصدارات متاحة")
                    
            except json.JSONDecodeError:
                print("  ❌ خطأ في تحليل الإصدارات")
        else:
            print(f"  ❌ فشل في الحصول على الإصدارات: {result['stderr']}")

    def attempt_service_fix(self):
        """محاولة إصلاح الخدمة"""
        print("\n🔧 محاولة إصلاح الخدمة...")
        
        fixes = [
            {
                "name": "إعادة نشر الخدمة",
                "command": f"gcloud run deploy {self.service_name} --source=. --region={self.region} --allow-unauthenticated --memory=2Gi --cpu=2",
                "description": "إعادة نشر الخدمة مع موارد محسنة"
            },
            {
                "name": "تحديث متغيرات البيئة",
                "command": f"gcloud run services update {self.service_name} --region={self.region} --set-env-vars=ENVIRONMENT=production",
                "description": "تحديث متغيرات البيئة"
            }
        ]
        
        for fix in fixes:
            print(f"\n🔄 تطبيق الإصلاح: {fix['name']}")
            print(f"   📝 الوصف: {fix['description']}")
            
            user_input = input(f"   ❓ هل تريد تطبيق هذا الإصلاح؟ (y/n): ").lower().strip()
            
            if user_input == 'y':
                print(f"   🚀 تنفيذ: {fix['command']}")
                result = self.run_gcloud_command(fix["command"])
                
                if result["success"]:
                    print(f"   ✅ تم تطبيق الإصلاح بنجاح")
                    if result["stdout"]:
                        print(f"   📄 النتيجة: {result['stdout'][:200]}...")
                else:
                    print(f"   ❌ فشل في تطبيق الإصلاح: {result['stderr']}")
            else:
                print(f"   ⏭️ تم تخطي هذا الإصلاح")

    def generate_diagnostic_report(self):
        """إنشاء تقرير التشخيص"""
        report_file = f"cloud_service_diagnostic_{self.timestamp}.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(f"# 🔧 تقرير تشخيص خدمة Google Cloud\n")
            f.write(f"## التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"### معلومات الخدمة:\n")
            f.write(f"- **المشروع**: {self.project_id}\n")
            f.write(f"- **الخدمة**: {self.service_name}\n")
            f.write(f"- **المنطقة**: {self.region}\n")
            f.write(f"- **الرابط**: https://{self.service_name}-554716410816.{self.region}.run.app\n\n")
            f.write(f"### المشكلة المكتشفة:\n")
            f.write(f"- **الحالة**: Service Unavailable\n")
            f.write(f"- **الوصف**: الخدمة غير متاحة حالياً\n\n")
            f.write(f"### الحلول المقترحة:\n")
            f.write(f"1. إعادة نشر الخدمة مع موارد محسنة\n")
            f.write(f"2. فحص السجلات للأخطاء\n")
            f.write(f"3. تحديث متغيرات البيئة\n")
            f.write(f"4. زيادة الموارد المخصصة\n\n")
        
        print(f"\n💾 تم حفظ تقرير التشخيص: {report_file}")

    def run_full_diagnostic(self):
        """تشغيل التشخيص الكامل"""
        self.print_banner()
        
        print("🔍 بدء التشخيص الشامل...")
        print("=" * 70)
        
        # فحص المصادقة
        auth_ok = self.check_gcloud_auth()
        
        # فحص إعداد المشروع
        project_ok = self.check_project_config()
        
        if not auth_ok or not project_ok:
            print("\n❌ يجب إصلاح مشاكل المصادقة والإعداد أولاً")
            return
        
        # فحص حالة الخدمة
        service_data = self.check_service_status()
        
        # فحص السجلات
        self.check_service_logs()
        
        # فحص الإصدارات
        self.check_service_revisions()
        
        # محاولة الإصلاح
        self.attempt_service_fix()
        
        # إنشاء التقرير
        self.generate_diagnostic_report()
        
        print("\n" + "=" * 70)
        print("🎯 انتهى التشخيص. راجع التقرير للتفاصيل الكاملة.")

if __name__ == "__main__":
    diagnostic = CloudServiceDiagnostic()
    diagnostic.run_full_diagnostic()
