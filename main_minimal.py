import os
from flask import Flask

app = Flask(__name__)

@app.route('/')
def hello():
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Universal AI Assistants</title>
        <meta charset="UTF-8">
    </head>
    <body style="font-family: Arial; text-align: center; padding: 50px;">
        <h1>🤖 Universal AI Assistants</h1>
        <h2>✅ الخدمة تعمل بنجاح!</h2>
        <p>تم إصلاح المشكلة وإعادة تشغيل الخدمة</p>
        <p>📅 التاريخ: """ + str(__import__('datetime').datetime.now()) + """</p>
    </body>
    </html>
    """

@app.route('/health')
def health():
    return {'status': 'healthy', 'service': 'Universal AI Assistants'}

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8080))
    app.run(host='0.0.0.0', port=port)
