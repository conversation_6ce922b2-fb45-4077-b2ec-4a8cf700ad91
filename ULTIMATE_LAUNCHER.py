#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🚀 Universal AI Assistants - Ultimate Launcher
المشغل النهائي الشامل لجميع أنظمة المشروع

تاريخ الإنشاء: 2025-01-31
الإصدار: 3.0.0 Ultimate
الحالة: جاهز للإنتاج
"""

import os
import sys
import time
import json
import subprocess
from datetime import datetime
from pathlib import Path

class UltimateLauncher:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.systems = {
            "horus": "HORUS_AI_TEAM",
            "anubis": "ANUBIS_SYSTEM", 
            "mcp": "ANUBIS_HORUS_MCP"
        }
        
    def print_banner(self):
        """عرض شعار المشروع"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║    🚀 Universal AI Assistants - Ultimate Launcher 🚀        ║
║                                                              ║
║    𓅃 HORUS AI TEAM    🏺 ANUBIS SYSTEM    🔗 MCP PROTOCOL   ║
║                                                              ║
║    📊 97% Success Rate  |  🤖 9 AI Agents  |  🔧 75 Tools   ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
        print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📍 المسار: {self.project_root}")
        print("=" * 70)

    def check_system_status(self):
        """فحص حالة الأنظمة"""
        print("🔍 فحص حالة الأنظمة...")
        status = {}
        
        for name, path in self.systems.items():
            full_path = self.project_root / path
            if full_path.exists():
                file_count = len(list(full_path.rglob("*")))
                status[name] = {
                    "exists": True,
                    "path": str(full_path),
                    "files": file_count,
                    "status": "✅ متاح"
                }
            else:
                status[name] = {
                    "exists": False,
                    "status": "❌ غير موجود"
                }
        
        # عرض النتائج
        print("\n📊 حالة الأنظمة:")
        for name, info in status.items():
            print(f"  {name.upper()}: {info['status']}")
            if info.get('files'):
                print(f"    📁 الملفات: {info['files']}")
        
        return status

    def show_main_menu(self):
        """عرض القائمة الرئيسية"""
        menu = """
🎯 القائمة الرئيسية:

1️⃣  🚀 تشغيل سريع (Quick Start)
2️⃣  𓅃 تشغيل فريق حورس (HORUS AI Team)
3️⃣  🏺 تشغيل نظام أنوبيس (ANUBIS System)
4️⃣  🔗 تشغيل نظام MCP (MCP Protocol)
5️⃣  🌐 تشغيل جميع الأنظمة (All Systems)
6️⃣  🧪 اختبار شامل (Comprehensive Test)
7️⃣  📊 تقرير الحالة (Status Report)
8️⃣  🔧 أدوات التطوير (Development Tools)
9️⃣  📚 التوثيق والمساعدة (Documentation)
0️⃣  🚪 خروج (Exit)

        """
        print(menu)

    def quick_start(self):
        """تشغيل سريع للنظام"""
        print("🚀 بدء التشغيل السريع...")
        
        # تشغيل QUICK_START.py إذا كان موجوداً
        quick_start_path = self.project_root / "scripts" / "QUICK_START.py"
        if quick_start_path.exists():
            print("📂 تشغيل QUICK_START.py...")
            subprocess.run([sys.executable, str(quick_start_path)])
        else:
            print("⚠️ ملف QUICK_START.py غير موجود")
            self.launch_horus()

    def launch_horus(self):
        """تشغيل فريق حورس"""
        print("𓅃 تشغيل فريق حورس...")
        
        horus_path = self.project_root / "HORUS_AI_TEAM"
        if not horus_path.exists():
            print("❌ مجلد HORUS_AI_TEAM غير موجود")
            return
            
        # البحث عن ملفات التشغيل
        start_files = [
            "summon_horus_assistant.py",
            "quick_start_fixed.py", 
            "horus_direct_test.py"
        ]
        
        for file_name in start_files:
            file_path = horus_path / file_name
            if file_path.exists():
                print(f"📂 تشغيل {file_name}...")
                subprocess.run([sys.executable, str(file_path)])
                return
                
        print("⚠️ لم يتم العثور على ملفات تشغيل حورس")

    def launch_anubis(self):
        """تشغيل نظام أنوبيس"""
        print("🏺 تشغيل نظام أنوبيس...")
        
        anubis_path = self.project_root / "ANUBIS_SYSTEM"
        if not anubis_path.exists():
            print("❌ مجلد ANUBIS_SYSTEM غير موجود")
            return
            
        # البحث عن ملفات التشغيل
        start_files = [
            "main.py",
            "quick_start_anubis.py",
            "startup.py"
        ]
        
        for file_name in start_files:
            file_path = anubis_path / file_name
            if file_path.exists():
                print(f"📂 تشغيل {file_name}...")
                subprocess.run([sys.executable, str(file_path)])
                return
                
        print("⚠️ لم يتم العثور على ملفات تشغيل أنوبيس")

    def launch_mcp(self):
        """تشغيل نظام MCP"""
        print("🔗 تشغيل نظام MCP...")
        
        mcp_path = self.project_root / "ANUBIS_HORUS_MCP"
        if not mcp_path.exists():
            print("❌ مجلد ANUBIS_HORUS_MCP غير موجود")
            return
            
        # تشغيل النظام التعاوني
        collab_file = mcp_path / "collaborative_ai_system.py"
        if collab_file.exists():
            print("📂 تشغيل collaborative_ai_system.py...")
            subprocess.run([sys.executable, str(collab_file)])
        else:
            print("⚠️ ملف collaborative_ai_system.py غير موجود")

    def launch_all_systems(self):
        """تشغيل جميع الأنظمة"""
        print("🌐 تشغيل جميع الأنظمة...")
        
        # تشغيل INTEGRATE_ALL_PROJECTS.py إذا كان موجوداً
        integrate_path = self.project_root / "scripts" / "INTEGRATE_ALL_PROJECTS.py"
        if integrate_path.exists():
            print("📂 تشغيل INTEGRATE_ALL_PROJECTS.py...")
            subprocess.run([sys.executable, str(integrate_path)])
        else:
            print("⚠️ تشغيل الأنظمة بشكل منفصل...")
            self.launch_horus()
            time.sleep(2)
            self.launch_anubis()
            time.sleep(2)
            self.launch_mcp()

    def run_comprehensive_test(self):
        """تشغيل اختبار شامل"""
        print("🧪 تشغيل اختبار شامل...")
        
        test_file = self.project_root / "COMPREHENSIVE_TOOLS_TESTER.py"
        if test_file.exists():
            print("📂 تشغيل COMPREHENSIVE_TOOLS_TESTER.py...")
            subprocess.run([sys.executable, str(test_file)])
        else:
            print("⚠️ ملف الاختبار الشامل غير موجود")

    def show_status_report(self):
        """عرض تقرير الحالة"""
        print("📊 تقرير الحالة:")
        
        # قراءة آخر تقرير
        report_file = self.project_root / "FINAL_PROJECT_STATUS_REPORT.md"
        if report_file.exists():
            print(f"📄 آخر تقرير: {report_file}")
            print("📖 لقراءة التقرير الكامل، افتح الملف:")
            print(f"   {report_file}")
        else:
            print("⚠️ تقرير الحالة غير متوفر")
            
        # عرض إحصائيات سريعة
        status = self.check_system_status()
        total_systems = len(status)
        active_systems = sum(1 for s in status.values() if s.get('exists', False))
        
        print(f"\n📈 إحصائيات سريعة:")
        print(f"   🏗️ الأنظمة النشطة: {active_systems}/{total_systems}")
        print(f"   📁 إجمالي الملفات: {sum(s.get('files', 0) for s in status.values())}")

    def show_development_tools(self):
        """عرض أدوات التطوير"""
        print("🔧 أدوات التطوير المتاحة:")
        
        tools_dir = self.project_root / "scripts"
        if tools_dir.exists():
            tools = list(tools_dir.glob("*.py"))
            print(f"📂 مجلد الأدوات: {tools_dir}")
            print(f"🔧 عدد الأدوات: {len(tools)}")
            
            # عرض أهم الأدوات
            important_tools = [
                "HORUS_PROJECT_ANALYZER.py",
                "MODELS_STATUS_CHECKER.py", 
                "DEPLOY_TO_GITHUB.py",
                "COMPREHENSIVE_SYSTEM_TESTER.py"
            ]
            
            print("\n🌟 الأدوات المهمة:")
            for tool in important_tools:
                tool_path = tools_dir / tool
                if tool_path.exists():
                    print(f"   ✅ {tool}")
                else:
                    print(f"   ❌ {tool}")
        else:
            print("❌ مجلد الأدوات غير موجود")

    def show_documentation(self):
        """عرض التوثيق والمساعدة"""
        print("📚 التوثيق والمساعدة:")
        
        docs_dir = self.project_root / "PROJECT_DOCUMENTATION"
        if docs_dir.exists():
            docs = list(docs_dir.glob("*.md"))
            print(f"📂 مجلد التوثيق: {docs_dir}")
            print(f"📚 عدد الملفات: {len(docs)}")
            
            # عرض أهم الملفات
            important_docs = [
                "PROJECT_STRUCTURE_DETAILED.md",
                "USER_GUIDE_COMPLETE.md",
                "DEVELOPMENT_RULES.md",
                "DEPLOYMENT_GUIDE.md"
            ]
            
            print("\n📖 الملفات المهمة:")
            for doc in important_docs:
                doc_path = docs_dir / doc
                if doc_path.exists():
                    print(f"   ✅ {doc}")
                else:
                    print(f"   ❌ {doc}")
        else:
            print("❌ مجلد التوثيق غير موجود")

    def run(self):
        """تشغيل المشغل الرئيسي"""
        self.print_banner()
        
        # فحص الحالة
        self.check_system_status()
        
        while True:
            self.show_main_menu()
            
            try:
                choice = input("🎯 اختر رقماً (0-9): ").strip()
                
                if choice == "1":
                    self.quick_start()
                elif choice == "2":
                    self.launch_horus()
                elif choice == "3":
                    self.launch_anubis()
                elif choice == "4":
                    self.launch_mcp()
                elif choice == "5":
                    self.launch_all_systems()
                elif choice == "6":
                    self.run_comprehensive_test()
                elif choice == "7":
                    self.show_status_report()
                elif choice == "8":
                    self.show_development_tools()
                elif choice == "9":
                    self.show_documentation()
                elif choice == "0":
                    print("🚪 شكراً لاستخدام Universal AI Assistants!")
                    break
                else:
                    print("❌ اختيار غير صحيح. حاول مرة أخرى.")
                    
                print("\n" + "="*50)
                input("⏸️ اضغط Enter للمتابعة...")
                
            except KeyboardInterrupt:
                print("\n🚪 تم إيقاف البرنامج بواسطة المستخدم")
                break
            except Exception as e:
                print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    launcher = UltimateLauncher()
    launcher.run()
