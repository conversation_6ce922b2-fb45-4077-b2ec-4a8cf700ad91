# سكريبت النشر الطارئ - Universal AI Assistants

Write-Host "🚨 بدء النشر الطارئ..." -ForegroundColor Red

# نسخ الملفات الطارئة المبسطة
Copy-Item "main_emergency.py" "main.py" -Force
Copy-Item "requirements_emergency.txt" "requirements.txt" -Force

Write-Host "📋 إعداد المشروع..." -ForegroundColor Yellow
gcloud config set project universal-ai-assistants-2025

Write-Host "🐳 نشر الخدمة المبسطة..." -ForegroundColor Yellow
gcloud run deploy universal-ai-assistants `
  --source . `
  --region us-central1 `
  --allow-unauthenticated `
  --memory 1Gi `
  --cpu 1 `
  --max-instances 3 `
  --timeout 300 `
  --port 8080

Write-Host "✅ تم النشر الطارئ!" -ForegroundColor Green
Write-Host "🔗 اختبار الخدمة:" -ForegroundColor Cyan
gcloud run services describe universal-ai-assistants --region=us-central1 --format="value(status.url)"

Read-Host "اضغط Enter للمتابعة"
