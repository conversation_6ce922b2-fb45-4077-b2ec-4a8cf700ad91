#!/bin/bash
# سكريبت نشر سريع لـ Universal AI Assistants

echo "🚀 بدء النشر السريع..."

# تعيين المتغيرات
export PROJECT_ID="universal-ai-assistants-2025"
export SERVICE_NAME="universal-ai-assistants"
export REGION="us-central1"

echo "📋 إعداد المشروع..."
gcloud config set project $PROJECT_ID

echo "🔧 تفعيل الخدمات..."
gcloud services enable run.googleapis.com
gcloud services enable cloudbuild.googleapis.com

echo "🐳 نشر الخدمة..."
gcloud run deploy $SERVICE_NAME \
  --source . \
  --region $REGION \
  --allow-unauthenticated \
  --memory 2Gi \
  --cpu 2 \
  --max-instances 10 \
  --set-env-vars ENVIRONMENT=production \
  --set-env-vars PROJECT_ID=$PROJECT_ID

echo "✅ تم النشر بنجاح!"
echo "🔗 رابط الخدمة:"
gcloud run services describe $SERVICE_NAME --region=$REGION --format="value(status.url)"
