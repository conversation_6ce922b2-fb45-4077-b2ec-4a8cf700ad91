#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
⚡ THOTH - المحلل السريع
وكيل الذكاء الاصطناعي المتخصص في التحليل السريع والفوري

الإله المصري: تحوت - إله الحكمة والمعرفة والكتابة
التخصص: التحليل السريع، معالجة البيانات الفورية، الاستجابة السريعة
النموذج: phi3:mini (سريع وفعال)
"""

import time
import json
from datetime import datetime
from typing import Dict, List, Any, Optional

class THOTHQuickAnalyzer:
    """
    ⚡ تحوت - المحلل السريع
    وكيل متخصص في التحليل السريع والاستجابة الفورية
    """
    
    def __init__(self):
        self.name = "THOTH"
        self.title = "تحوت - المحلل السريع"
        self.symbol = "⚡"
        self.model = "phi3:mini"
        self.specialization = "التحليل السريع والاستجابة الفورية"
        self.capabilities = [
            "تحليل سريع للبيانات",
            "معالجة فورية للمعلومات", 
            "استخراج الأنماط بسرعة",
            "تقييم سريع للحالة",
            "إنتاج تقارير مختصرة"
        ]
        self.response_time_target = 2.0  # ثانية
        self.analysis_history = []
        
    def quick_analyze(self, data: Any, analysis_type: str = "general") -> Dict[str, Any]:
        """
        تحليل سريع للبيانات المدخلة
        
        Args:
            data: البيانات المراد تحليلها
            analysis_type: نوع التحليل (general, numerical, text, system)
            
        Returns:
            Dict: نتائج التحليل السريع
        """
        start_time = time.time()
        
        try:
            analysis_result = {
                "timestamp": datetime.now().isoformat(),
                "analysis_type": analysis_type,
                "data_type": type(data).__name__,
                "quick_insights": [],
                "summary": "",
                "confidence": 0.0,
                "processing_time": 0.0
            }
            
            # تحليل حسب نوع البيانات
            if isinstance(data, str):
                analysis_result.update(self._analyze_text(data))
            elif isinstance(data, (int, float)):
                analysis_result.update(self._analyze_number(data))
            elif isinstance(data, (list, tuple)):
                analysis_result.update(self._analyze_sequence(data))
            elif isinstance(data, dict):
                analysis_result.update(self._analyze_dict(data))
            else:
                analysis_result.update(self._analyze_general(data))
            
            # حساب وقت المعالجة
            processing_time = time.time() - start_time
            analysis_result["processing_time"] = round(processing_time, 3)
            
            # تقييم الأداء
            if processing_time <= self.response_time_target:
                analysis_result["performance"] = "ممتاز - ضمن الهدف"
            else:
                analysis_result["performance"] = "بطيء - يحتاج تحسين"
            
            # حفظ في التاريخ
            self.analysis_history.append(analysis_result)
            
            return analysis_result
            
        except Exception as e:
            return {
                "error": f"خطأ في التحليل: {str(e)}",
                "timestamp": datetime.now().isoformat(),
                "processing_time": time.time() - start_time
            }
    
    def _analyze_text(self, text: str) -> Dict[str, Any]:
        """تحليل سريع للنص"""
        words = text.split()
        chars = len(text)
        
        insights = [
            f"عدد الكلمات: {len(words)}",
            f"عدد الأحرف: {chars}",
            f"متوسط طول الكلمة: {chars/len(words):.1f}" if words else "نص فارغ"
        ]
        
        # تحليل المحتوى
        if any(word in text.lower() for word in ['خطأ', 'error', 'فشل', 'fail']):
            insights.append("⚠️ يحتوي على مؤشرات خطأ")
        
        if any(word in text.lower() for word in ['نجح', 'success', 'تم', 'complete']):
            insights.append("✅ يحتوي على مؤشرات نجاح")
        
        return {
            "quick_insights": insights,
            "summary": f"نص من {len(words)} كلمة و {chars} حرف",
            "confidence": 0.9
        }
    
    def _analyze_number(self, number: float) -> Dict[str, Any]:
        """تحليل سريع للرقم"""
        insights = [
            f"القيمة: {number}",
            f"النوع: {'صحيح' if isinstance(number, int) else 'عشري'}",
            f"الإشارة: {'موجب' if number > 0 else 'سالب' if number < 0 else 'صفر'}"
        ]
        
        # تصنيف الرقم
        if abs(number) < 1:
            insights.append("رقم صغير")
        elif abs(number) > 1000:
            insights.append("رقم كبير")
        
        return {
            "quick_insights": insights,
            "summary": f"رقم {number} ({'موجب' if number > 0 else 'سالب' if number < 0 else 'صفر'})",
            "confidence": 1.0
        }
    
    def _analyze_sequence(self, sequence: List) -> Dict[str, Any]:
        """تحليل سريع للقائمة"""
        length = len(sequence)
        
        insights = [
            f"عدد العناصر: {length}",
            f"النوع: {type(sequence).__name__}"
        ]
        
        if length > 0:
            first_type = type(sequence[0]).__name__
            insights.append(f"نوع العنصر الأول: {first_type}")
            
            # فحص التجانس
            if all(type(item) == type(sequence[0]) for item in sequence):
                insights.append("✅ عناصر متجانسة")
            else:
                insights.append("⚠️ عناصر غير متجانسة")
        
        return {
            "quick_insights": insights,
            "summary": f"قائمة من {length} عنصر",
            "confidence": 0.8
        }
    
    def _analyze_dict(self, data_dict: Dict) -> Dict[str, Any]:
        """تحليل سريع للقاموس"""
        keys_count = len(data_dict.keys())
        
        insights = [
            f"عدد المفاتيح: {keys_count}",
            f"المفاتيح: {list(data_dict.keys())[:5]}" + ("..." if keys_count > 5 else "")
        ]
        
        # تحليل أنواع القيم
        value_types = set(type(v).__name__ for v in data_dict.values())
        insights.append(f"أنواع القيم: {list(value_types)}")
        
        return {
            "quick_insights": insights,
            "summary": f"قاموس بـ {keys_count} مفتاح",
            "confidence": 0.85
        }
    
    def _analyze_general(self, data: Any) -> Dict[str, Any]:
        """تحليل عام للبيانات"""
        insights = [
            f"النوع: {type(data).__name__}",
            f"الحجم: {len(str(data))} حرف في التمثيل النصي"
        ]
        
        return {
            "quick_insights": insights,
            "summary": f"بيانات من نوع {type(data).__name__}",
            "confidence": 0.6
        }
    
    def system_status_check(self) -> Dict[str, Any]:
        """فحص سريع لحالة النظام"""
        return {
            "agent": self.name,
            "status": "نشط",
            "model": self.model,
            "analyses_performed": len(self.analysis_history),
            "average_response_time": self._calculate_average_response_time(),
            "capabilities": self.capabilities,
            "last_analysis": self.analysis_history[-1]["timestamp"] if self.analysis_history else "لا يوجد"
        }
    
    def _calculate_average_response_time(self) -> float:
        """حساب متوسط وقت الاستجابة"""
        if not self.analysis_history:
            return 0.0
        
        times = [analysis.get("processing_time", 0) for analysis in self.analysis_history]
        return round(sum(times) / len(times), 3)
    
    def get_performance_report(self) -> Dict[str, Any]:
        """تقرير أداء سريع"""
        total_analyses = len(self.analysis_history)
        avg_time = self._calculate_average_response_time()
        
        # تصنيف الأداء
        if avg_time <= 1.0:
            performance_grade = "ممتاز"
        elif avg_time <= 2.0:
            performance_grade = "جيد"
        elif avg_time <= 5.0:
            performance_grade = "مقبول"
        else:
            performance_grade = "يحتاج تحسين"
        
        return {
            "agent": self.title,
            "total_analyses": total_analyses,
            "average_response_time": avg_time,
            "target_response_time": self.response_time_target,
            "performance_grade": performance_grade,
            "efficiency": f"{(self.response_time_target / max(avg_time, 0.1)) * 100:.1f}%"
        }
    
    def quick_response(self, query: str) -> str:
        """استجابة سريعة لاستفسار"""
        start_time = time.time()
        
        # تحليل سريع للاستفسار
        query_lower = query.lower()
        
        if "حالة" in query_lower or "status" in query_lower:
            response = f"⚡ {self.title} نشط وجاهز. أداء ممتاز مع متوسط استجابة {self._calculate_average_response_time():.2f} ثانية."
        
        elif "تحليل" in query_lower or "analyze" in query_lower:
            response = "⚡ جاهز للتحليل السريع! أرسل البيانات وسأحللها فوراً."
        
        elif "مساعدة" in query_lower or "help" in query_lower:
            response = f"⚡ {self.title} - متخصص في:\n" + "\n".join(f"• {cap}" for cap in self.capabilities)
        
        else:
            # تحليل سريع للاستفسار نفسه
            analysis = self.quick_analyze(query, "text")
            response = f"⚡ تحليل سريع: {analysis.get('summary', 'تم التحليل')}"
        
        processing_time = time.time() - start_time
        response += f"\n⏱️ وقت الاستجابة: {processing_time:.3f} ثانية"
        
        return response

# إنشاء مثيل الوكيل
thoth_agent = THOTHQuickAnalyzer()

def get_agent():
    """إرجاع مثيل الوكيل"""
    return thoth_agent

def quick_analyze(data, analysis_type="general"):
    """دالة سريعة للتحليل"""
    return thoth_agent.quick_analyze(data, analysis_type)

def get_status():
    """الحصول على حالة الوكيل"""
    return thoth_agent.system_status_check()

if __name__ == "__main__":
    # اختبار الوكيل
    print(f"⚡ {thoth_agent.title}")
    print("=" * 50)
    
    # اختبار التحليل السريع
    test_data = [
        "هذا نص تجريبي للاختبار",
        42,
        [1, 2, 3, 4, 5],
        {"name": "test", "value": 100}
    ]
    
    for data in test_data:
        result = thoth_agent.quick_analyze(data)
        print(f"\n📊 تحليل: {data}")
        print(f"   النتيجة: {result['summary']}")
        print(f"   الوقت: {result['processing_time']} ثانية")
    
    # تقرير الأداء
    print(f"\n📈 تقرير الأداء:")
    performance = thoth_agent.get_performance_report()
    for key, value in performance.items():
        print(f"   {key}: {value}")
