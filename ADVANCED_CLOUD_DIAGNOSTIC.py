#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 تشخيص متقدم لمشكلة Service Unavailable
تاريخ الإنشاء: 2025-08-01
الهدف: حل مشكلة Service Unavailable المستمرة
"""

import subprocess
import json
import time
from datetime import datetime
from pathlib import Path

class AdvancedCloudDiagnostic:
    def __init__(self):
        self.project_id = "universal-ai-assistants-2025"
        self.service_name = "universal-ai-assistants"
        self.region = "us-central1"
        self.old_url = "https://universal-ai-assistants-554716410816.us-central1.run.app"
        self.new_url = "https://universal-ai-assistants-mcurffhera-uc.a.run.app"
        
    def print_banner(self):
        """عرض شعار التشخيص المتقدم"""
        banner = """
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║         🔍 تشخيص متقدم لمشكلة Cloud Service 🔍              ║
║                                                              ║
║    ⚡ Advanced Diagnostic - Service Unavailable             ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
        """
        print(banner)
        print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🔗 الرابط القديم: {self.old_url}")
        print(f"🔗 الرابط الجديد: {self.new_url}")
        print("=" * 70)

    def analyze_deployment_logs(self):
        """تحليل سجلات النشر"""
        print("📋 تحليل سجلات النشر...")
        
        # قراءة آخر سجلات Cloud Build
        command = f'gcloud logging read "resource.type=build AND resource.labels.build_id~universal-ai-assistants" --limit=5 --format=json'
        result = self.run_gcloud_command(command)
        
        if result["success"] and result["stdout"]:
            try:
                logs = json.loads(result["stdout"])
                print(f"  📊 عدد سجلات البناء: {len(logs)}")
                
                for log in logs:
                    timestamp = log.get("timestamp", "غير معروف")
                    severity = log.get("severity", "INFO")
                    text = log.get("textPayload", "")
                    
                    if "ERROR" in severity or "FAILED" in text.upper():
                        print(f"  🔴 خطأ في البناء: {text[:100]}...")
                    elif "SUCCESS" in text.upper() or "COMPLETE" in text.upper():
                        print(f"  ✅ نجح البناء: {text[:100]}...")
                        
            except json.JSONDecodeError:
                print("  ❌ خطأ في تحليل سجلات البناء")
        else:
            print("  ⚠️ لا توجد سجلات بناء متاحة")

    def check_service_health(self):
        """فحص صحة الخدمة"""
        print("🏥 فحص صحة الخدمة...")
        
        # فحص الخدمة الجديدة
        command = f"gcloud run services describe {self.service_name} --region={self.region} --format=json"
        result = self.run_gcloud_command(command)
        
        if result["success"]:
            try:
                service_data = json.loads(result["stdout"])
                
                # فحص الحالة
                status = service_data.get("status", {})
                conditions = status.get("conditions", [])
                
                print(f"  📊 معلومات الخدمة الجديدة:")
                print(f"    🔗 URL: {status.get('url', 'غير متاح')}")
                
                # تحليل الشروط
                ready_condition = None
                for condition in conditions:
                    condition_type = condition.get("type", "Unknown")
                    condition_status = condition.get("status", "Unknown")
                    reason = condition.get("reason", "")
                    message = condition.get("message", "")
                    
                    status_icon = "✅" if condition_status == "True" else "❌"
                    print(f"    {status_icon} {condition_type}: {condition_status}")
                    
                    if condition_type == "Ready":
                        ready_condition = condition
                    
                    if reason and reason != "":
                        print(f"      📝 السبب: {reason}")
                    if message and message != "":
                        print(f"      💬 الرسالة: {message}")
                
                # تحليل مشكلة Ready condition
                if ready_condition and ready_condition.get("status") != "True":
                    print(f"\n  🔍 تحليل مشكلة الجاهزية:")
                    reason = ready_condition.get("reason", "")
                    message = ready_condition.get("message", "")
                    
                    if "ContainerMissing" in reason:
                        print(f"    🐳 المشكلة: الحاوية مفقودة أو لم يتم بناؤها بشكل صحيح")
                    elif "RevisionFailed" in reason:
                        print(f"    📦 المشكلة: فشل في إنشاء الإصدار")
                    elif "ConfigurationError" in reason:
                        print(f"    ⚙️ المشكلة: خطأ في التكوين")
                    else:
                        print(f"    ❓ مشكلة غير محددة: {reason}")
                
                return service_data
                
            except json.JSONDecodeError:
                print("  ❌ خطأ في تحليل بيانات الخدمة")
                return None
        else:
            print(f"  ❌ فشل في الحصول على معلومات الخدمة: {result['stderr']}")
            return None

    def check_container_logs(self):
        """فحص سجلات الحاوية"""
        print("🐳 فحص سجلات الحاوية...")
        
        # فحص سجلات الخدمة الحديثة
        command = f'gcloud logs read "resource.type=cloud_run_revision AND resource.labels.service_name={self.service_name}" --limit=20 --format=json'
        result = self.run_gcloud_command(command)
        
        if result["success"] and result["stdout"]:
            try:
                logs = json.loads(result["stdout"])
                print(f"  📊 عدد السجلات: {len(logs)}")
                
                error_count = 0
                startup_errors = []
                
                for log in logs:
                    severity = log.get("severity", "INFO")
                    text_payload = log.get("textPayload", "")
                    timestamp = log.get("timestamp", "")
                    
                    if severity in ["ERROR", "CRITICAL"]:
                        error_count += 1
                        print(f"  🔴 خطأ: {text_payload[:150]}...")
                        
                        # تحليل أخطاء البدء الشائعة
                        if "ModuleNotFoundError" in text_payload:
                            startup_errors.append("مكتبة مفقودة")
                        elif "ImportError" in text_payload:
                            startup_errors.append("خطأ في الاستيراد")
                        elif "SyntaxError" in text_payload:
                            startup_errors.append("خطأ في بناء الجملة")
                        elif "Port" in text_payload and "bind" in text_payload:
                            startup_errors.append("مشكلة في المنفذ")
                        elif "Permission denied" in text_payload:
                            startup_errors.append("مشكلة في الصلاحيات")
                
                print(f"  📊 إجمالي الأخطاء: {error_count}")
                
                if startup_errors:
                    print(f"  🔍 أخطاء البدء المكتشفة:")
                    for error in set(startup_errors):
                        print(f"    - {error}")
                
                return {"error_count": error_count, "startup_errors": startup_errors}
                
            except json.JSONDecodeError:
                print("  ❌ خطأ في تحليل السجلات")
                return None
        else:
            print("  ⚠️ لا توجد سجلات متاحة")
            return None

    def create_minimal_working_app(self):
        """إنشاء تطبيق مبسط يعمل بالتأكيد"""
        print("🛠️ إنشاء تطبيق مبسط يعمل بالتأكيد...")
        
        # إنشاء main.py مبسط جداً
        minimal_app = '''import os
from flask import Flask

app = Flask(__name__)

@app.route('/')
def hello():
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Universal AI Assistants</title>
        <meta charset="UTF-8">
    </head>
    <body style="font-family: Arial; text-align: center; padding: 50px;">
        <h1>🤖 Universal AI Assistants</h1>
        <h2>✅ الخدمة تعمل بنجاح!</h2>
        <p>تم إصلاح المشكلة وإعادة تشغيل الخدمة</p>
        <p>📅 التاريخ: """ + str(__import__('datetime').datetime.now()) + """</p>
    </body>
    </html>
    """

@app.route('/health')
def health():
    return {'status': 'healthy', 'service': 'Universal AI Assistants'}

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 8080))
    app.run(host='0.0.0.0', port=port)
'''
        
        try:
            with open('main_minimal.py', 'w', encoding='utf-8') as f:
                f.write(minimal_app)
            print("  ✅ تم إنشاء main_minimal.py")
        except Exception as e:
            print(f"  ❌ خطأ في إنشاء التطبيق المبسط: {e}")

        # إنشاء requirements.txt مبسط
        minimal_requirements = '''Flask==2.3.3
gunicorn==21.2.0
'''
        
        try:
            with open('requirements_minimal.txt', 'w', encoding='utf-8') as f:
                f.write(minimal_requirements)
            print("  ✅ تم إنشاء requirements_minimal.txt")
        except Exception as e:
            print(f"  ❌ خطأ في إنشاء المتطلبات المبسطة: {e}")

        # إنشاء Dockerfile مبسط
        minimal_dockerfile = '''FROM python:3.9-slim

WORKDIR /app

COPY requirements_minimal.txt .
RUN pip install --no-cache-dir -r requirements_minimal.txt

COPY main_minimal.py main.py

EXPOSE 8080

CMD ["gunicorn", "--bind", "0.0.0.0:8080", "main:app"]
'''
        
        try:
            with open('Dockerfile_minimal', 'w', encoding='utf-8') as f:
                f.write(minimal_dockerfile)
            print("  ✅ تم إنشاء Dockerfile_minimal")
        except Exception as e:
            print(f"  ❌ خطأ في إنشاء Dockerfile: {e}")

    def create_emergency_deploy_script(self):
        """إنشاء سكريبت نشر طارئ"""
        print("🚨 إنشاء سكريبت نشر طارئ...")
        
        emergency_script = f'''# سكريبت النشر الطارئ - Universal AI Assistants

Write-Host "🚨 بدء النشر الطارئ..." -ForegroundColor Red

# نسخ الملفات المبسطة
Copy-Item "main_minimal.py" "main.py" -Force
Copy-Item "requirements_minimal.txt" "requirements.txt" -Force
Copy-Item "Dockerfile_minimal" "Dockerfile" -Force

Write-Host "📋 إعداد المشروع..." -ForegroundColor Yellow
gcloud config set project {self.project_id}

Write-Host "🐳 نشر الخدمة المبسطة..." -ForegroundColor Yellow
gcloud run deploy {self.service_name} `
  --source . `
  --region {self.region} `
  --allow-unauthenticated `
  --memory 1Gi `
  --cpu 1 `
  --max-instances 3 `
  --timeout 300 `
  --port 8080

Write-Host "✅ تم النشر الطارئ!" -ForegroundColor Green
Write-Host "🔗 اختبار الخدمة:" -ForegroundColor Cyan
gcloud run services describe {self.service_name} --region={self.region} --format="value(status.url)"

Read-Host "اضغط Enter للمتابعة"
'''
        
        try:
            with open('emergency_deploy.ps1', 'w', encoding='utf-8') as f:
                f.write(emergency_script)
            print("  ✅ تم إنشاء emergency_deploy.ps1")
        except Exception as e:
            print(f"  ❌ خطأ في إنشاء سكريبت النشر الطارئ: {e}")

    def run_gcloud_command(self, command):
        """تشغيل أمر gcloud وإرجاع النتيجة"""
        try:
            result = subprocess.run(
                command, 
                shell=True, 
                capture_output=True, 
                text=True, 
                timeout=60
            )
            return {
                "success": result.returncode == 0,
                "stdout": result.stdout.strip(),
                "stderr": result.stderr.strip(),
                "returncode": result.returncode
            }
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "stdout": "",
                "stderr": "Command timed out",
                "returncode": -1
            }
        except Exception as e:
            return {
                "success": False,
                "stdout": "",
                "stderr": str(e),
                "returncode": -1
            }

    def generate_diagnostic_report(self):
        """إنشاء تقرير التشخيص المتقدم"""
        report_content = f"""# 🔍 تقرير التشخيص المتقدم - Service Unavailable

## معلومات الخدمة
- **المشروع**: {self.project_id}
- **الخدمة**: {self.service_name}
- **المنطقة**: {self.region}
- **الرابط القديم**: {self.old_url}
- **الرابط الجديد**: {self.new_url}

## المشكلة المستمرة
- **الحالة**: Service Unavailable (مستمرة بعد إعادة النشر)
- **التاريخ**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## التشخيص المتقدم
### الأسباب المحتملة:
1. **مشكلة في الكود**: خطأ في main.py أو التطبيق
2. **مشكلة في المتطلبات**: مكتبات مفقودة أو متعارضة
3. **مشكلة في المنفذ**: التطبيق لا يستمع على المنفذ الصحيح
4. **مشكلة في البدء**: التطبيق يفشل في البدء
5. **مشكلة في الحاوية**: Docker container لا يعمل

## الحل الطارئ
تم إنشاء تطبيق مبسط مضمون العمل:
- `main_minimal.py` - تطبيق Flask بسيط جداً
- `requirements_minimal.txt` - متطلبات أساسية فقط
- `Dockerfile_minimal` - حاوية مبسطة
- `emergency_deploy.ps1` - نشر طارئ

## خطوات الإصلاح الطارئ
```powershell
.\\emergency_deploy.ps1
```

---
**تاريخ الإنشاء**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        try:
            with open('ADVANCED_DIAGNOSTIC_REPORT.md', 'w', encoding='utf-8') as f:
                f.write(report_content)
            print("💾 تم حفظ تقرير التشخيص المتقدم: ADVANCED_DIAGNOSTIC_REPORT.md")
        except Exception as e:
            print(f"❌ خطأ في حفظ التقرير: {e}")

    def run_advanced_diagnostic(self):
        """تشغيل التشخيص المتقدم"""
        self.print_banner()
        
        print("🔍 بدء التشخيص المتقدم للمشكلة المستمرة...")
        print("=" * 70)
        
        # تحليل سجلات النشر
        self.analyze_deployment_logs()
        
        # فحص صحة الخدمة
        service_data = self.check_service_health()
        
        # فحص سجلات الحاوية
        container_logs = self.check_container_logs()
        
        # إنشاء تطبيق مبسط
        self.create_minimal_working_app()
        
        # إنشاء سكريبت نشر طارئ
        self.create_emergency_deploy_script()
        
        # إنشاء التقرير
        self.generate_diagnostic_report()
        
        print("\n" + "=" * 70)
        print("🎯 انتهى التشخيص المتقدم.")
        print("\n📋 الخطوات الطارئة:")
        print("   1. شغل النشر الطارئ: .\\emergency_deploy.ps1")
        print("   2. انتظر اكتمال النشر (3-5 دقائق)")
        print("   3. اختبر الخدمة على الرابط الجديد")
        print("\n🔗 هذا سيضمن عمل الخدمة بأبسط شكل ممكن")

if __name__ == "__main__":
    diagnostic = AdvancedCloudDiagnostic()
    diagnostic.run_advanced_diagnostic()
